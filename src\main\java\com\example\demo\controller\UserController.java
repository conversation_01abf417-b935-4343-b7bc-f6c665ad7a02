package com.example.demo.controller;

import com.example.demo.dto.ErrorResponse;
import com.example.demo.model.User;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")
public class UserController {

    @GetMapping("/profile")
    public ResponseEntity<?> getUserProfile(Authentication authentication) {
        try {
            if (authentication == null || authentication.getName() == null) {
                return ResponseEntity.badRequest().body(new ErrorResponse("Authentication required"));
            }

            User user = new User();
            user.setUsername(authentication.getName());
            user.setEmail(authentication.getName() + "@example.com");

            return ResponseEntity.ok(user);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new ErrorResponse("Internal server error"));
        }
    }

    @GetMapping("/list")
    public ResponseEntity<List<User>> getAllUsers() {
        User user1 = new User();
        user1.setUsername("admin");
        user1.setEmail("<EMAIL>");
        
        User user2 = new User();
        user2.setUsername("user");
        user2.setEmail("<EMAIL>");
        
        return ResponseEntity.ok(Arrays.asList(user1, user2));
    }
}