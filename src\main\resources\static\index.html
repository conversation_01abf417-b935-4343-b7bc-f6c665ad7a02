<!DOCTYPE html>
<html>
<head>
    <title>JWT Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; }
        input, button { padding: 10px; margin: 5px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h2>JWT Authentication Demo</h2>
        
        <div id="loginSection">
            <h3>Login</h3>
            <input type="text" id="username" placeholder="Username (admin)" value="admin">
            <input type="password" id="password" placeholder="Password (password)" value="password">
            <button onclick="login()">Login</button>
        </div>

        <div id="apiSection" style="display:none;">
            <h3>Protected APIs</h3>
            <button onclick="getUserProfile()">Get Profile</button>
            <button onclick="getUserList()">Get User List</button>
            <button onclick="logout()">Logout</button>
        </div>

        <div id="result" class="result"></div>
    </div>

    <script>
        let token = localStorage.getItem('jwt_token');
        
        if (token) {
            showApiSection();
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    token = data.token;
                    localStorage.setItem('jwt_token', token);
                    showResult('Login successful!', data);
                    showApiSection();
                } else {
                    showResult('Login failed!', data);
                }
            } catch (error) {
                showResult('Error:', error.message);
            }
        }

        async function getUserProfile() {
            try {
                const response = await fetch('/api/users/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                showResult('User Profile:', data);
            } catch (error) {
                showResult('Error:', error.message);
            }
        }

        async function getUserList() {
            try {
                const response = await fetch('/api/users/list', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                showResult('User List:', data);
            } catch (error) {
                showResult('Error:', error.message);
            }
        }

        function logout() {
            token = null;
            localStorage.removeItem('jwt_token');
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('apiSection').style.display = 'none';
            showResult('Logged out successfully!');
        }

        function showApiSection() {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('apiSection').style.display = 'block';
        }

        function showResult(message, data = null) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<strong>${message}</strong>`;
            if (data) {
                resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
        }
    </script>
</body>
</html>